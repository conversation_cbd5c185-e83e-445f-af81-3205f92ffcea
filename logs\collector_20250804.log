2025-08-04 02:33:11,942 - INFO - DataCollector资源清理完成
2025-08-04 02:42:49,786 - INFO - 设置筛选条件: {'日期': '本周 (2025-08-04-2025-08-10)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:42:49,787 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:42:49,811 - INFO - 类目数据加载成功
2025-08-04 02:42:49,812 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-08-04", "currentEndDay": "2025-08-10", "compareStartDay": "2025-07-28", "compareEndDay": "2025-08-03", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:42:49,816 - INFO - 成功加载 8 个Cookie
2025-08-04 02:42:50,492 - WARNING - 响应中没有数据
2025-08-04 02:42:58,494 - INFO - 设置筛选条件: {'日期': '本周 (2025-08-04-2025-08-10)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:42:58,494 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:42:58,517 - INFO - 类目数据加载成功
2025-08-04 02:42:58,518 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-08-04", "currentEndDay": "2025-08-10", "compareStartDay": "2025-07-28", "compareEndDay": "2025-08-03", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:42:58,520 - INFO - 成功加载 8 个Cookie
2025-08-04 02:42:59,216 - WARNING - 响应中没有数据
2025-08-04 02:43:03,020 - INFO - 设置筛选条件: {'日期': '2025-07-28-2025-08-03', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:43:03,021 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:43:03,045 - INFO - 类目数据加载成功
2025-08-04 02:43:03,045 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:43:03,047 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:04,431 - INFO - 成功获取 100 条基础数据
2025-08-04 02:43:04,432 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 02:43:04,437 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:04,437 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:04,440 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:05,406 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:05,451 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:05,572 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:11,597 - INFO - DataCollector资源清理完成
2025-08-04 02:56:01,373 - INFO - DataCollector资源清理完成
2025-08-04 02:56:31,804 - INFO - 设置筛选条件: {'日期': '本周 (2025-08-04-2025-08-10)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:56:31,805 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:56:31,829 - INFO - 类目数据加载成功
2025-08-04 02:56:31,830 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-08-04", "currentEndDay": "2025-08-10", "compareStartDay": "2025-07-28", "compareEndDay": "2025-08-03", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:56:31,833 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:32,512 - WARNING - 响应中没有数据
2025-08-04 02:56:37,564 - INFO - 设置筛选条件: {'日期': '2025-07-28-2025-08-03', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:56:37,565 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:56:37,588 - INFO - 类目数据加载成功
2025-08-04 02:56:37,589 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:56:37,590 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:38,231 - INFO - 成功获取 100 条基础数据
2025-08-04 02:56:38,231 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 02:56:38,235 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:38,236 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:38,236 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,075 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,092 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,098 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,950 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,966 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,981 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:40,953 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:40,980 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:40,980 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:42,885 - INFO - DataCollector资源清理完成
2025-08-04 09:14:53,117 - INFO - DataCollector资源清理完成
2025-08-04 09:15:22,572 - INFO - DataCollector资源清理完成
2025-08-04 09:15:30,029 - INFO - DataCollector资源清理完成
2025-08-04 20:25:13,294 - INFO - DataCollector资源清理完成
